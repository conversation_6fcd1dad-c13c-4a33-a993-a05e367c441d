package com.hdec.data.controller;

import com.hdec.common.domain.R;
import com.hdec.data.service.OnlineImportService;
import com.hdec.data.vo.OnlineImportListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 在线数据录入控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "在线数据录入")
@Validated
@RestController
@RequestMapping("api/data/onlineImport")
public class OnlineImportController {

    @Autowired
    private OnlineImportService importService;

    /**
     * 列表
     */
    @ApiOperation("列表")
    @PostMapping("list")
    public R list(@RequestBody OnlineImportListVo vo) {
        R r = importService.list(vo.getTime(), vo.getPointIds(), vo.getPointNos(), vo.getRate());
        return r;
    }

    /**
     * 保存
     */
    @ApiOperation("保存")
    @PostMapping("save/{rate}")
    public R save(@RequestHeader("fieldNum") String fieldNum, @RequestHeader(value = "sessionId") String sessionId,
                  @PathVariable("rate") String rate,
                  @RequestBody List<Map<String, String>> list) throws Exception {
        // 检查是否为高频数据，如果是则抛出错误
        String decodedRate = rate;
        try {
            decodedRate = java.net.URLDecoder.decode(rate, "UTF-8");
        } catch (Exception e) {
            // 如果解码失败，使用原始值
        }

        if ("高频".equals(rate) || "高频".equals(decodedRate) || "%E9%AB%98%E9%A2%91".equals(rate)) {
            return R.error("无法录入高频数据");
        }

        importService.save(sessionId, fieldNum, rate, list);
        log.info("保存数据:{}",list);
        return R.success("保存成功");
    }

}
