package com.hdec.data.domain;

import com.hdec.common.domain.AlarmRuleVo;
import com.hdec.common.domain.AttrCommon;
import com.hdec.common.domain.FormulaCommon;
import com.hdec.common.domain.PointCommon;
import com.hdec.common.vo.AlarmAdvancedRuleVo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 数据录入信息
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ImportInfo {

    /** 风场下所有测点 */
    private Map<Integer, PointCommon> fieldIdPoints;

    /** 风场下所有测点 */
    private Map<String, PointCommon> fieldNoPoints;

    /** 风场下所有参数 */
    private Map<String, String> fieldParams;

    /** 风场下所有属性 */
    private Map<Integer, AttrCommon> fieldAttrs;

    /** 风场下所有公式 */
    private List<FormulaCommon> fieldFormulas;

    /** 匹配关系 */
    private Map<String, Map<Integer, String>> sheetColTypeMap;

    /** Excel内容 */
    private Map<String, List<Map<Integer, Object>>> sheets;

    /** 风场下所有告警规则 */
    private Map<String, AlarmRuleVo> ruleMap;
    private Map<String, AlarmAdvancedRuleVo> ruleAdvancedMap;

    private Map<Integer, Map<Integer, String>> errMsgs;

    public ImportInfo(Map<Integer, PointCommon> fieldIdPoints, Map<String, PointCommon> fieldNoPoints, Map<String, String> fieldParams, Map<Integer, AttrCommon> fieldAttrs,
                      List<FormulaCommon> fieldFormulas, Map<String, Map<Integer, String>> sheetColTypeMap,
                      Map<String, List<Map<Integer, Object>>> sheets, Map<String, AlarmRuleVo> ruleMap, Map<String, AlarmAdvancedRuleVo> ruleAdvancedMap, Map<Integer, Map<Integer, String>> errMsgs) {
        this.fieldIdPoints = fieldIdPoints;
        this.fieldNoPoints = fieldNoPoints;
        this.fieldParams = fieldParams;
        this.fieldAttrs = fieldAttrs;
        this.fieldFormulas = fieldFormulas;
        this.sheetColTypeMap = sheetColTypeMap;
        this.sheets = sheets;
        this.ruleMap = ruleMap;
        this.ruleAdvancedMap = ruleAdvancedMap;
        this.errMsgs = errMsgs;
    }
}
