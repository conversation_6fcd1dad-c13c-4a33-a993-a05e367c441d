package com.hdec.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 *
 * @date 2023/2/23
 */
@Data
@ToString
@NoArgsConstructor
public class InsertRecord implements Serializable {

    private Integer instId;

    private String time;

    private Integer pointId;

    private Integer directId;

    private Set<AttrIdVal> values;

    private AttrIdVal value;

    private String rate;

    public InsertRecord(Integer instId, String time, Integer pointId, Integer directId, AttrIdVal value, String rate) {
        this.instId = instId;
        this.time = time;
        this.pointId = pointId;
        this.directId = directId;
        this.value = value;
        this.rate = rate;
    }

    public InsertRecord(Integer instId, String time, Integer pointId, Integer directId, Set<AttrIdVal> values, String rate) {
        this.instId = instId;
        this.time = time;
        this.pointId = pointId;
        this.directId = directId;
        this.values = values;
        this.rate = rate;
    }

    public InsertRecord(Integer instId, String time, Integer pointId, Integer directId, AttrIdVal value, Set<AttrIdVal> values, String rate) {
        this.instId = instId;
        this.time = time;
        this.pointId = pointId;
        this.directId = directId;
        this.value = value;
        this.values = values;
        this.rate = rate;
    }

    public InsertRecord(String time, Integer pointId, Integer directId, Set<AttrIdVal> values, AttrIdVal value) {
        this.time = time;
        this.pointId = pointId;
        this.directId = directId;
        this.values = values;
        this.value = value;
    }
}
