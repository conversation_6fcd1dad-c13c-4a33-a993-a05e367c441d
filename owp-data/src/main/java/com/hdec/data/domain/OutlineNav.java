package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 报告大纲导航实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class OutlineNav {

    /** 自增主键 */
    private Integer id;

    /** 名称 */
    private String name;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone="GMT+8")
    private Date createTime;

    /** 父节点ID */
    private Integer pid = 0;

    /** 所属大纲ID */
    private Integer outlineId;

    /** 目标节点ID */
    private Integer navId;

    /** 排序 */
    private Integer order;

    /** 是否告警 */
    private Boolean isWarning = false;

    /** 风场编码 */
    private String fieldNum;

    public OutlineNav(Integer id, String name, Integer pid, Integer outlineId) {
        this.id = id;
        this.name = name;
        this.pid = pid;
        this.outlineId = outlineId;
    }
}
