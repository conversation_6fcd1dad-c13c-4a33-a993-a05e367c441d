package com.hdec.data.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hdec.common.util.TimeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 任务
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class Task {

    /** 自增主键 */
    private Integer id;

    /** 任务类型 */
    private String type;

    /** 任务级别 */
    private String level;

    /** 参数 */
    private String params;

    /** 频率 */
    private String rate;

    /** 用户ID */
    private Integer userId;

    /** 用户名 */
    private String username;

    /** 状态（0：未开始  1：进行中  2：已完成） */
    private Integer status;

    /** 失败信息 */
    private String errMsg;

    /** 进度 */
    private Integer process;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date finishTime;

    /** 所属风场 */
    private String fieldNum;

    /** 风场名称 */
    private String fieldName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date reStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date reEndTime;

    /** 等待时长 */
    private String wait;

    /** 耗时 */
    private String cost;

    public Task(String type, Integer userId, String username, String fieldNum) {
        this.type = type;
        this.userId = userId;
        this.username = username;
        this.fieldNum = fieldNum;
    }

    public Task(String type, Integer userId, String username, Integer status, String fieldNum) {
        this.type = type;
        this.userId = userId;
        this.username = username;
        this.status = status;
        this.createTime = new Date();
        this.startTime = new Date();
        this.process = 0;
        this.fieldNum = fieldNum;
    }

    public Task(String type, String level, String params, String rate, Integer userId, String username, String fieldNum) {
        this.type = type;
        this.level = level;
        this.params = params;
        this.rate = rate;
        this.userId = userId;
        this.username = username;
        this.fieldNum = fieldNum;
        this.process = 0;
        this.status = 0;
        this.createTime = new Date();
    }

    public String getWait() {
        if (createTime == null || startTime == null) {
            return null;
        }
        return TimeUtil.millis2Human(startTime.getTime() - createTime.getTime());
    }

    public String getCost() {
        if (startTime == null || finishTime == null) {
            return null;
        }
        return TimeUtil.millis2Human(finishTime.getTime() - startTime.getTime());
    }
}
